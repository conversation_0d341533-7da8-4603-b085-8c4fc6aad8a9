"""
Memory Update Module for Two-Phase Pipeline

Processes candidate memories through LLM-based operation decisions
with vector similarity search for conflict resolution.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from llm_cache_service import get_llm_cache_service, CacheType
from config import get_config
from exceptions import (
    MemoryUpdateError, EmbeddingGenerationError, DatabaseQueryError,
    LLMResponseError, VectorSearchError, handle_known_exception
)

logger = logging.getLogger(__name__)
config = get_config()

class MemoryUpdateModule:
    """
    Update phase of the two-phase memory pipeline.
    
    Uses LLM to decide on ADD/UPDATE/DELETE/NOOP operations based on
    vector similarity with existing memories.
    """
    
    def __init__(self, llm_client, db_client, bge_client, supabase_mcp=None):
        """
        Initialize update module.
        
        Args:
            llm_client: LLM client for operation decisions
            db_client: Database client for memory storage
            bge_client: BGE embedding client
            supabase_mcp: Supabase MCP context for database operations
        """
        self.llm_client = llm_client
        self.db = db_client
        self.bge = bge_client
        self.supabase_mcp = supabase_mcp
        self.cache_service = get_llm_cache_service()
        
        # Similarity threshold for conflict detection (from research)
        self.similarity_threshold = config.memory.SIMILARITY_THRESHOLD
        
        logger.info("Initialized MemoryUpdateModule with LLM caching")
    
    async def process_candidate_memory_with_embedding(
        self, 
        candidate: str, 
        embedding: List[float], 
        user_id: str
    ) -> Dict[str, Any]:
        """
        Process candidate memory with pre-computed embedding for performance.
        
        Args:
            candidate: Candidate memory string to process
            embedding: Pre-computed embedding vector
            user_id: User identifier
            
        Returns:
            Dictionary with operation result and metadata
        """
        try:
            # Find similar existing memories using provided embedding
            similar_memories = await self._find_similar_memories(
                embedding, user_id, self.similarity_threshold
            )
            
            # LLM decides operation based on similarity
            operation_result = await self._decide_operation(candidate, similar_memories)
            
            # Execute the decided operation
            execution_result = await self._execute_operation(
                operation_result, candidate, embedding, user_id
            )
            
            result = {
                'candidate': candidate,
                'operation': operation_result['operation'],
                'reasoning': operation_result['reasoning'],
                'similar_count': len(similar_memories),
                'execution_result': execution_result,
                'user_id': user_id
            }
            
            logger.debug(f"Processed candidate memory with operation: {operation_result['operation']}")
            return result
            
        except (MemoryUpdateError, EmbeddingGenerationError, DatabaseQueryError, VectorSearchError) as e:
            logger.error(f"Known error processing candidate memory: {e}")
            return {
                'candidate': candidate,
                'operation': 'ERROR',
                'reasoning': str(e),
                'similar_count': 0,
                'execution_result': None,
                'user_id': user_id
            }
        except Exception as e:
            logger.error(f"Unexpected error processing candidate memory: {e}")
            # Convert to appropriate exception
            known_exception = handle_known_exception(e)
            return {
                'candidate': candidate,
                'operation': 'ERROR',
                'reasoning': f"Unexpected error: {str(known_exception)}",
                'similar_count': 0,
                'execution_result': None,
                'user_id': user_id
            }
    
    async def process_candidate_memories_batch(
        self, 
        candidates: List[str], 
        user_id: str,
        batch_size: int = None
    ) -> List[Dict[str, Any]]:
        """
        Process multiple candidate memories efficiently using batch operations.
        
        This leverages the enhanced BGE client's batch capabilities for better performance.
        
        Args:
            candidates: List of candidate memory strings to process
            user_id: User identifier
            batch_size: Batch size for embedding generation
            
        Returns:
            List of operation results for each candidate
        """
        if not candidates:
            return []
        
        if batch_size is None:
            batch_size = config.performance.DEFAULT_BATCH_SIZE
        
        try:
            # Generate embeddings for all candidates at once (much faster)
            logger.debug(f"Generating embeddings for {len(candidates)} candidates")
            embeddings = await self.bge.embed_documents(candidates)  # Use documents (no instruction)
            
            # Process each candidate with its pre-computed embedding
            results = []
            for candidate, embedding in zip(candidates, embeddings):
                result = await self.process_candidate_memory_with_embedding(
                    candidate, embedding, user_id
                )
                results.append(result)
            
            logger.info(f"Batch processed {len(candidates)} candidate memories")
            return results
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            # Fallback to individual processing
            results = []
            for candidate in candidates:
                result = await self.process_candidate_memory(candidate, user_id)
                results.append(result)
            return results
    
    async def process_candidate_memory(self, candidate: str, user_id: str) -> Dict[str, Any]:
        """
        Process candidate memory through update pipeline.
        
        Args:
            candidate: Candidate memory string to process
            user_id: User identifier
            
        Returns:
            Dictionary with operation result and metadata
        """
        try:
            # Generate embedding for similarity search
            embedding = await self.bge.embed_single(candidate, add_instruction=False)
            
            # Use the optimized method with pre-computed embedding
            return await self.process_candidate_memory_with_embedding(
                candidate, embedding, user_id
            )
            
        except Exception as e:
            logger.error(f"Error processing candidate memory: {e}")
            return {
                'candidate': candidate,
                'operation': 'ERROR', 
                'reasoning': str(e),
                'similar_count': 0,
                'execution_result': None,
                'user_id': user_id
            }
    
    async def _find_similar_memories(
        self, 
        embedding: List[float], 
        user_id: str, 
        threshold: float
    ) -> List[Dict[str, Any]]:
        """
        Find similar existing memories using optimized vector similarity search.
        
        Args:
            embedding: Query embedding vector
            user_id: User identifier
            threshold: Similarity threshold
            
        Returns:
            List of similar memories with distance scores
        """
        try:
            # Use database client's optimized similarity search
            results = await self.db.similarity_search(embedding, user_id, threshold, 5)
            return results
            
        except Exception as e:
            logger.error(f"Error finding similar memories: {e}")
            return []
    
    async def _decide_operation(
        self, 
        candidate: str, 
        similar_memories: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        LLM decides ADD/UPDATE/DELETE/NOOP operation.
        
        Args:
            candidate: Candidate memory string
            similar_memories: List of similar existing memories
            
        Returns:
            Dictionary with operation decision and reasoning
        """
        try:
            # Build decision prompt
            prompt = self._build_decision_prompt(candidate, similar_memories)
            
            # Create cache context for operation decision
            cache_context = {
                "candidate_length": len(candidate),
                "similar_count": len(similar_memories),
                "similarity_scores": [mem.get('distance', 1.0) for mem in similar_memories[:config.database.RECENT_MEMORIES_LIMIT]]
            }
            
            # Check cache first for performance optimization
            cached_response = await self.cache_service.get_cached_response(
                CacheType.DECISION, prompt, cache_context
            )
            
            if cached_response:
                logger.debug("Using cached decision response")
                response = cached_response
            else:
                # Generate decision using LLM
                logger.debug("Generating new decision response")
                response = await self.llm_client.generate(prompt)
                
                # Cache the response for future use
                if response and response.strip():
                    await self.cache_service.cache_response(
                        CacheType.DECISION, prompt, response, cache_context, 
                        custom_ttl_hours=config.cache.DECISION_CACHE_TTL // 3600
                    )
            
            # Parse response
            try:
                result = json.loads(response)
                operation = result.get('operation', 'ADD').upper()
                reasoning = result.get('reasoning', 'No reasoning provided')
            except json.JSONDecodeError:
                # Fallback parsing
                response_upper = response.strip().upper()
                if 'UPDATE' in response_upper:
                    operation = 'UPDATE'
                elif 'DELETE' in response_upper:
                    operation = 'DELETE'
                elif 'NOOP' in response_upper:
                    operation = 'NOOP'
                else:
                    operation = 'ADD'
                reasoning = response.strip()
            
            # Validate operation
            if operation not in ['ADD', 'UPDATE', 'DELETE', 'NOOP']:
                logger.warning(f"Invalid operation '{operation}', defaulting to ADD")
                operation = 'ADD'
                reasoning = f"Invalid operation, defaulting to ADD. Original: {reasoning}"
            
            return {
                'operation': operation,
                'reasoning': reasoning
            }
            
        except Exception as e:
            logger.error(f"Error in operation decision: {e}")
            return {
                'operation': 'ADD',
                'reasoning': f"Error in decision process: {e}"
            }
    
    def _build_decision_prompt(
        self, 
        candidate: str, 
        similar_memories: List[Dict[str, Any]]
    ) -> str:
        """
        Build prompt for LLM operation decision.
        
        Args:
            candidate: Candidate memory string
            similar_memories: List of similar existing memories
            
        Returns:
            Formatted decision prompt
        """
        similar_content = []
        for mem in similar_memories[:3]:  # Limit to top 3 for context
            distance = mem.get('distance', 0)
            content = mem.get('content', '')
            similar_content.append(f"Distance {distance:.3f}: {content}")
        
        similar_text = "\\n".join(similar_content) if similar_content else "No similar memories found"
        
        prompt = f"""
Analyze this candidate memory and decide what operation to perform based on existing similar memories.

Candidate Memory: {candidate}

Similar Existing Memories:
{similar_text}

Operations:
- ADD: Create new memory (no conflicts, new information, or complementary to existing)
- UPDATE: Enhance existing memory (similar memory exists that can be improved/expanded)
- DELETE: Remove contradicted memory (candidate contradicts or makes existing memory obsolete)
- NOOP: No action needed (duplicate, irrelevant, or already well-covered)

Decision Criteria:
- If distance < {config.memory.VERY_SIMILAR_THRESHOLD}: Very similar, likely NOOP or UPDATE
- If distance {config.memory.VERY_SIMILAR_THRESHOLD}-{config.memory.MODERATELY_SIMILAR_THRESHOLD}: Moderately similar, consider UPDATE or ADD
- If distance {config.memory.MODERATELY_SIMILAR_THRESHOLD}-{config.memory.SOMEWHAT_SIMILAR_THRESHOLD}: Somewhat similar, likely ADD unless contradictory
- If no similar memories: ADD

Respond with JSON format:
{{"operation": "ADD|UPDATE|DELETE|NOOP", "reasoning": "Brief explanation of decision"}}
        """.strip()
        
        return prompt
    
    async def _execute_operation(
        self, 
        operation_result: Dict[str, str], 
        candidate: str, 
        embedding: List[float], 
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Execute the decided operation.
        
        Args:
            operation_result: Operation decision result
            candidate: Candidate memory string
            embedding: Memory embedding
            user_id: User identifier
            
        Returns:
            Execution result or None for NOOP
        """
        operation = operation_result['operation']
        
        try:
            if operation == 'ADD':
                return await self._execute_add(candidate, embedding, user_id)
                
            elif operation == 'UPDATE':
                return await self._execute_update(candidate, embedding, user_id)
                
            elif operation == 'DELETE':
                return await self._execute_delete(candidate, user_id)
                
            elif operation == 'NOOP':
                logger.debug("NOOP operation - no action taken")
                return None
                
        except Exception as e:
            logger.error(f"Error executing {operation} operation: {e}")
            return {'error': str(e)}
    
    async def _execute_add(self, content: str, embedding: List[float], user_id: str) -> Dict[str, Any]:
        """Execute ADD operation."""
        try:
            # Store new memory via real Supabase MCP
            if self.supabase_mcp:
                memory_id = await self.supabase_mcp.store_memory(user_id, content, embedding)
            else:
                memory_id = await self.db.store_memory(user_id, content, embedding)
            
            logger.info(f"Added new memory {memory_id} for user {user_id}")
            return {
                'action': 'added',
                'memory_id': memory_id,
                'content': content
            }
            
        except Exception as e:
            logger.error(f"Error in ADD execution: {e}")
            raise
    
    async def _execute_update(self, content: str, embedding: List[float], user_id: str) -> Dict[str, Any]:
        """Execute UPDATE operation."""
        try:
            # Find the most similar memory to update
            similar_memories = await self._find_similar_memories(
                embedding, user_id, config.memory.UPDATE_OPERATION_THRESHOLD  # Lower threshold for update
            )
            
            if not similar_memories:
                # No memory to update, fall back to ADD
                return await self._execute_add(content, embedding, user_id)
            
            # Update the most similar memory
            target_memory = similar_memories[0]
            memory_id = target_memory['id']
            
            # Merge content (simple approach - could be enhanced with LLM)
            updated_content = f"{target_memory['content']}. Updated: {content}"
            
            # Update via Supabase MCP
            sql = """
                UPDATE memories 
                SET content = $2, embedding = $3, updated_at = NOW()
                WHERE id = $1
            """
            
            # Execute via Supabase MCP (placeholder for now)
            
            logger.info(f"Updated memory {memory_id} for user {user_id}")
            return {
                'action': 'updated',
                'memory_id': memory_id,
                'old_content': target_memory['content'],
                'new_content': updated_content
            }
            
        except Exception as e:
            logger.error(f"Error in UPDATE execution: {e}")
            raise
    
    async def _execute_delete(self, content: str, user_id: str) -> Dict[str, Any]:
        """Execute DELETE operation."""
        try:
            # Generate embedding to find memory to delete
            embedding = await self.bge.embed_single(content, add_instruction=False)
            
            # Find similar memories
            similar_memories = await self._find_similar_memories(
                embedding, user_id, config.memory.DELETE_OPERATION_THRESHOLD  # High threshold for deletion
            )
            
            if not similar_memories:
                logger.warning("No similar memory found for deletion")
                return {'action': 'no_deletion', 'reason': 'No similar memory found'}
            
            # Delete the most similar memory
            target_memory = similar_memories[0]
            memory_id = target_memory['id']
            
            # Delete via Supabase MCP
            sql = "DELETE FROM memories WHERE id = $1"
            
            # Execute via Supabase MCP (placeholder for now)
            
            logger.info(f"Deleted memory {memory_id} for user {user_id}")
            return {
                'action': 'deleted',
                'memory_id': memory_id,
                'deleted_content': target_memory['content']
            }
            
        except Exception as e:
            logger.error(f"Error in DELETE execution: {e}")
            raise