"""
Memory Handler Functions for MCP Tools

Extracted helper functions to reduce complexity in main MCP tool handlers.
Follows single responsibility principle for improved maintainability.
"""

import time
import json
import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple

from config import get_config
from exceptions import (
    MCPProtocolError, MemoryExtractionError, EmbeddingGenerationError,
    DatabaseQueryError, create_error_response, SparkMemoryError
)

logger = logging.getLogger(__name__)
config = get_config()


async def build_extraction_context(memory_system, user_id: str) -> Dict[str, Any]:
    """
    Build context for memory extraction with performance tracking.
    
    Args:
        memory_system: The memory system context
        user_id: User identifier
        
    Returns:
        Context dictionary for extraction
        
    Raises:
        DatabaseQueryError: If context building fails
    """
    try:
        context_start = time.time()
        
        context = {
            "conversation_summary": await memory_system.summary.get_conversation_summary(user_id),
            "recent_messages": await memory_system.db.get_recent_memories(
                user_id, 
                limit=config.database.RECENT_MEMORIES_LIMIT
            ),
            "user_id": user_id
        }
        
        context_time = (time.time() - context_start) * 1000
        logger.info(f"Context building took {context_time:.0f}ms")
        
        return context
        
    except Exception as e:
        raise DatabaseQueryError(
            f"Failed to build extraction context: {str(e)}",
            user_id=user_id
        )


async def extract_memory_candidates(memory_system, text: str, context: Dict[str, Any]) -> List[str]:
    """
    Extract memory candidates from text using Phase 1 pipeline.
    
    Args:
        memory_system: The memory system context
        text: Input text to extract memories from
        context: Extraction context
        
    Returns:
        List of extracted memory candidates
        
    Raises:
        MemoryExtractionError: If extraction fails
    """
    try:
        # Create message pair (simplified - could be enhanced with conversation history)
        message_pair = ("", text)  # Empty previous message for single input
        
        # Phase 1: Extraction with performance tracking
        extraction_start = time.time()
        candidates = await memory_system.extraction.extract_memories(message_pair, context)
        extraction_time = (time.time() - extraction_start) * 1000
        
        logger.info(f"Phase 1 extraction: {extraction_time:.0f}ms, {len(candidates)} candidates")
        
        return candidates
        
    except Exception as e:
        raise MemoryExtractionError(
            f"Memory extraction failed: {str(e)}",
            llm_response=None,
            parsing_method="two_phase_pipeline"
        )


async def generate_batch_embeddings(memory_system, candidates: List[str]) -> Optional[List[List[float]]]:
    """
    Generate embeddings for all candidates using batch processing.
    
    Args:
        memory_system: The memory system context
        candidates: List of candidate memory strings
        
    Returns:
        List of embedding vectors, or None if batch generation fails
        
    Raises:
        EmbeddingGenerationError: If embedding generation fails critically
    """
    if not candidates:
        return []
    
    embedding_start = time.time()
    
    try:
        candidate_embeddings = await memory_system.bge.embed_texts(
            candidates, add_instruction=False
        )
        embedding_time = (time.time() - embedding_start) * 1000
        
        logger.info(f"BGE embedding generation: {embedding_time:.0f}ms, {len(candidate_embeddings)} embeddings")
        
        return candidate_embeddings
        
    except Exception as e:
        embedding_time = (time.time() - embedding_start) * 1000
        logger.error(f"Batch embedding failed after {embedding_time:.0f}ms, falling back to individual: {e}")
        
        # Return None to indicate fallback should be used
        return None


async def process_candidates_with_embeddings(
    memory_system, 
    candidates: List[str], 
    embeddings: List[List[float]], 
    user_id: str
) -> List[Dict[str, Any]]:
    """
    Process candidates using pre-computed embeddings.
    
    Args:
        memory_system: The memory system context
        candidates: List of candidate memory strings
        embeddings: Pre-computed embedding vectors
        user_id: User identifier
        
    Returns:
        List of processing results
    """
    candidate_tasks = [
        memory_system.update.process_candidate_memory_with_embedding(
            candidate, embedding, user_id
        )
        for candidate, embedding in zip(candidates, embeddings)
    ]
    
    return await execute_candidate_processing(candidate_tasks, len(candidates))


async def process_candidates_individually(
    memory_system, 
    candidates: List[str], 
    user_id: str
) -> List[Dict[str, Any]]:
    """
    Process candidates individually (fallback method).
    
    Args:
        memory_system: The memory system context
        candidates: List of candidate memory strings
        user_id: User identifier
        
    Returns:
        List of processing results
    """
    candidate_tasks = [
        memory_system.update.process_candidate_memory(candidate, user_id)
        for candidate in candidates
    ]
    
    return await execute_candidate_processing(candidate_tasks, len(candidates))


async def execute_candidate_processing(
    candidate_tasks: List, 
    candidate_count: int
) -> List[Dict[str, Any]]:
    """
    Execute candidate processing tasks with timeout and error handling.
    
    Args:
        candidate_tasks: List of async tasks to execute
        candidate_count: Number of candidates being processed
        
    Returns:
        List of processing results
    """
    try:
        # Execute in parallel with timeout
        results = await asyncio.wait_for(
            asyncio.gather(*candidate_tasks, return_exceptions=True), 
            timeout=config.performance.OPERATION_TIMEOUT
        )
    except asyncio.TimeoutError:
        logger.error(f"Candidate processing timed out after {config.performance.OPERATION_TIMEOUT} seconds")
        return [{"operation": "TIMEOUT", "reasoning": "Processing timed out"}] * candidate_count
    
    # Handle exceptions in parallel processing
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Error processing candidate {i}: {result}")
            processed_results.append({"operation": "ERROR", "reasoning": str(result)})
        else:
            processed_results.append(result)
    
    return processed_results


async def process_memory_pipeline(memory_system, text: str, user_id: str) -> Dict[str, Any]:
    """
    Execute the complete two-phase memory pipeline.
    
    Args:
        memory_system: The memory system context
        text: Input text to process
        user_id: User identifier
        
    Returns:
        Dictionary with processing results
        
    Raises:
        SparkMemoryError: If any phase of processing fails
    """
    # Build context for extraction
    context = await build_extraction_context(memory_system, user_id)
    
    # Phase 1: Extract memory candidates
    candidates = await extract_memory_candidates(memory_system, text, context)
    
    # Phase 2: Process candidates if any were extracted
    results = []
    if candidates:
        logger.debug(f"Processing {len(candidates)} candidates in parallel")
        
        # Try batch embedding generation first
        candidate_embeddings = await generate_batch_embeddings(memory_system, candidates)
        
        if candidate_embeddings and len(candidate_embeddings) == len(candidates):
            # Use pre-computed embeddings for faster processing
            results = await process_candidates_with_embeddings(
                memory_system, candidates, candidate_embeddings, user_id
            )
        else:
            # Fallback to individual processing
            results = await process_candidates_individually(
                memory_system, candidates, user_id
            )
    
    # Update rolling summary asynchronously (non-blocking)
    asyncio.create_task(
        memory_system.summary.update_summary_async(user_id, [text])
    )
    
    return {
        "success": True,
        "candidates_processed": len(candidates),
        "operations": [r["operation"] for r in results],
        "user_id": user_id
    }


def create_context_access_error_response(context_error: Exception, user_id: str, function_name: str) -> Dict[str, Any]:
    """
    Create standardized error response for context access failures.
    
    Args:
        context_error: The context access exception
        user_id: User identifier
        function_name: Name of the function where error occurred
        
    Returns:
        Standardized error response dictionary
    """
    return {
        "success": False,
        "error": "context_access_error", 
        "message": f"Context access failed: {str(context_error)}",
        "function": function_name,
        "user_id": user_id,
        "candidates_processed": 0,
        "operations": []
    }


def create_memory_operation_error_response(
    exception: Exception, 
    user_id: str, 
    function_name: str
) -> Dict[str, Any]:
    """
    Create standardized error response for memory operations.
    
    Args:
        exception: The exception that occurred
        user_id: User identifier  
        function_name: Name of the function where error occurred
        
    Returns:
        Standardized error response dictionary
    """
    if isinstance(exception, SparkMemoryError):
        error_response = create_error_response(exception, function_name)
        error_response["user_id"] = user_id
        return error_response
    
    # Handle unknown exceptions
    from exceptions import handle_known_exception
    spark_exception = handle_known_exception(exception)
    error_response = create_error_response(spark_exception, function_name)
    error_response["user_id"] = user_id
    
    return error_response


async def get_memory_system_context(ctx, function_name: str):
    """
    Safely access memory system context with proper error handling.
    
    Args:
        ctx: MCP context
        function_name: Name of the calling function
        
    Returns:
        Memory system context
        
    Raises:
        MCPProtocolError: If context access fails
    """
    try:
        return ctx.request_context.lifespan_context
    except Exception as context_error:
        logger.error(f"Context access failed in {function_name}: {context_error}")
        raise MCPProtocolError(
            f"Failed to access memory system context: {str(context_error)}",
            mcp_tool=function_name,
            context_error=str(context_error)
        )