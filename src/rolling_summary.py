"""
Rolling Summary Manager for Conversation Context

Implements asynchronous conversation summary generation and management
for enhanced memory extraction context.
"""

import asyncio
import json
import logging
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class RollingSummaryManager:
    """
    Manages conversation summaries for memory context.
    
    Provides asynchronous summary updates and context caching
    for the two-phase memory pipeline.
    """
    
    def __init__(self, llm_client, db_client, supabase_mcp=None):
        """
        Initialize rolling summary manager.
        
        Args:
            llm_client: LLM client for summary generation
            db_client: Database client for summary storage
            supabase_mcp: Supabase MCP context for database operations
        """
        self.llm_client = llm_client
        self.db = db_client
        self.supabase_mcp = supabase_mcp
        
        # In-memory cache for immediate access
        self.summary_cache = {}
        
        # Configuration
        self.max_summary_length = 2000
        self.update_interval = 10  # messages
        self.context_window = 20  # recent messages to consider
        
        logger.info("Initialized RollingSummaryManager")
    
    async def get_conversation_summary(self, user_id: str) -> str:
        """
        Get current conversation summary for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Current conversation summary or empty string if none exists
        """
        try:
            # Check cache first
            if user_id in self.summary_cache:
                return self.summary_cache[user_id]
            
            # Load from database
            summary = await self._load_summary_from_db(user_id)
            
            if not summary:
                # Generate initial summary if none exists
                summary = await self._generate_initial_summary(user_id)
            
            # Cache the summary
            if summary:
                self.summary_cache[user_id] = summary
            
            return summary or ""
            
        except Exception as e:
            logger.error(f"Error getting conversation summary: {e}")
            return ""
    
    async def update_summary_async(self, user_id: str, new_messages: List[str]):
        """
        Asynchronously update conversation summary with new messages.
        
        This runs in the background without blocking the main pipeline.
        
        Args:
            user_id: User identifier
            new_messages: List of new message contents
        """
        # Run in background task to avoid blocking
        asyncio.create_task(
            self._update_summary_background(user_id, new_messages)
        )
    
    async def _update_summary_background(self, user_id: str, new_messages: List[str]):
        """
        Background task for summary updates.
        
        Args:
            user_id: User identifier
            new_messages: List of new message contents
        """
        try:
            # Get current summary
            current_summary = await self.get_conversation_summary(user_id)
            
            # Generate updated summary
            updated_summary = await self._generate_updated_summary(
                current_summary, new_messages, user_id
            )
            
            if updated_summary:
                # Update cache
                self.summary_cache[user_id] = updated_summary
                
                # Store in database
                await self._store_summary_in_db(user_id, updated_summary)
                
                logger.debug(f"Updated conversation summary for user {user_id}")
            
        except Exception as e:
            # Log error but don't crash main pipeline
            logger.error(f"Error in background summary update: {e}")
    
    async def _generate_initial_summary(self, user_id: str) -> str:
        """
        Generate initial summary from recent memories.
        
        Args:
            user_id: User identifier
            
        Returns:
            Initial conversation summary
        """
        try:
            # Get recent memories for context
            recent_memories = await self.db.get_recent_memories(user_id, 10)
            
            if not recent_memories:
                return ""
            
            # Build initial summary prompt
            prompt = self._build_initial_summary_prompt(recent_memories)
            
            # Generate summary
            summary = await self.llm_client.generate(prompt)
            
            # Clean and validate summary
            summary = self._clean_summary(summary)
            
            logger.debug(f"Generated initial summary for user {user_id}")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating initial summary: {e}")
            return ""
    
    async def _generate_updated_summary(
        self, 
        current_summary: str, 
        new_messages: List[str], 
        user_id: str
    ) -> str:
        """
        Generate updated summary incorporating new messages.
        
        Args:
            current_summary: Current conversation summary
            new_messages: New messages to incorporate
            user_id: User identifier
            
        Returns:
            Updated conversation summary
        """
        try:
            # Build update prompt
            prompt = self._build_update_summary_prompt(
                current_summary, new_messages
            )
            
            # Generate updated summary
            updated_summary = await self.llm_client.generate(prompt)
            
            # Clean and validate summary
            updated_summary = self._clean_summary(updated_summary)
            
            return updated_summary
            
        except Exception as e:
            logger.error(f"Error generating updated summary: {e}")
            return current_summary  # Return current summary on error
    
    def _build_initial_summary_prompt(self, recent_memories: List[str]) -> str:
        """
        Build prompt for initial summary generation.
        
        Args:
            recent_memories: List of recent memory contents
            
        Returns:
            Formatted prompt
        """
        memory_text = "\\n".join(recent_memories)
        
        prompt = f"""
Create a concise conversation summary based on these key memories:

Key Memories:
{memory_text}

Generate a summary that captures:
- Main topics of discussion
- User preferences and background information
- Important decisions or conclusions
- Ongoing projects or interests
- Key relationships or context

Keep the summary under {self.max_summary_length} characters and focus on information that would be valuable for future conversations.

Summary:
        """.strip()
        
        return prompt
    
    def _build_update_summary_prompt(
        self, 
        current_summary: str, 
        new_messages: List[str]
    ) -> str:
        """
        Build prompt for summary updates.
        
        Args:
            current_summary: Current conversation summary
            new_messages: New messages to incorporate
            
        Returns:
            Formatted update prompt
        """
        new_message_text = "\\n".join(new_messages)
        
        prompt = f"""
Update the conversation summary by incorporating new information from recent messages.

Current Summary:
{current_summary}

New Messages:
{new_message_text}

Instructions:
- Preserve important existing information
- Add significant new information from the messages
- Remove outdated or contradicted information
- Maintain conciseness (under {self.max_summary_length} characters)
- Focus on facts, preferences, and context that aid future conversations

Updated Summary:
        """.strip()
        
        return prompt
    
    def _clean_summary(self, summary: str) -> str:
        """
        Clean and validate summary text.
        
        Args:
            summary: Raw summary text
            
        Returns:
            Cleaned summary
        """
        if not summary:
            return ""
        
        # Clean whitespace
        summary = summary.strip()
        
        # Truncate if too long
        if len(summary) > self.max_summary_length:
            summary = summary[:self.max_summary_length - 3] + "..."
        
        # Remove common prefixes
        prefixes = ["Summary:", "Updated Summary:", "Conversation Summary:"]
        for prefix in prefixes:
            if summary.startswith(prefix):
                summary = summary[len(prefix):].strip()
        
        return summary
    
    async def _load_summary_from_db(self, user_id: str) -> Optional[str]:
        """
        Load summary from database via Supabase MCP.
        
        Args:
            user_id: User identifier
            
        Returns:
            Stored summary or None if not found
        """
        try:
            # Execute via Supabase MCP
            sql = "SELECT summary FROM conversation_summaries WHERE user_id = $1"
            
            # This would be executed via MCP in actual implementation
            # For now, return None as placeholder
            return None
            
        except Exception as e:
            logger.error(f"Error loading summary from database: {e}")
            return None
    
    async def _store_summary_in_db(self, user_id: str, summary: str):
        """
        Store summary in database via Supabase MCP.
        
        Args:
            user_id: User identifier
            summary: Summary text to store
        """
        try:
            # Execute via Supabase MCP
            sql = """
                INSERT INTO conversation_summaries (user_id, summary, updated_at)
                VALUES ($1, $2, NOW())
                ON CONFLICT (user_id) 
                DO UPDATE SET summary = $2, updated_at = NOW()
            """
            
            # This would be executed via MCP in actual implementation
            logger.debug(f"Stored summary for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error storing summary in database: {e}")
    
    def clear_cache(self, user_id: Optional[str] = None):
        """
        Clear summary cache.
        
        Args:
            user_id: Specific user to clear, or None to clear all
        """
        if user_id:
            self.summary_cache.pop(user_id, None)
            logger.debug(f"Cleared cache for user {user_id}")
        else:
            self.summary_cache.clear()
            logger.debug("Cleared all summary cache")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """
        Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            'cached_users': len(self.summary_cache),
            'total_cache_size': sum(len(summary) for summary in self.summary_cache.values())
        }