"""
Performance Monitor for Memory Operations

Tracks performance metrics against LOCOMO benchmark targets:
- Latency: p50 < 0.708s, p95 < 1.440s (91% reduction target)
- Token Usage: ~7K tokens per conversation (90% savings target)  
- Accuracy: 26% improvement in memory retrieval tasks
"""

import time
import statistics
import logging
from typing import Dict, Any, List, Callable, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """
    Monitors and tracks performance of memory operations.
    
    Validates performance against research targets from LOCOMO benchmarks.
    """
    
    def __init__(self):
        """Initialize performance monitor."""
        self.metrics = {
            "latency_samples": [],
            "token_usage": [],
            "memory_operations": [],
            "accuracy_metrics": [],
            "session_stats": {}
        }
        
        # LOCOMO benchmark targets from research
        self.targets = {
            "latency_p50_ms": 708,    # 0.708s
            "latency_p95_ms": 1440,   # 1.440s  
            "tokens_per_conversation": 7000,  # ~7K tokens
            "accuracy_improvement": 0.26  # 26% improvement
        }
        
        # Session tracking
        self.session_start = time.time()
        self.current_session = datetime.now().isoformat()
        
        logger.info("Initialized PerformanceMonitor with LOCOMO targets")
    
    async def track_operation(
        self, 
        operation_name: str, 
        func: Callable, 
        *args, 
        **kwargs
    ) -> Any:
        """
        Track performance of memory operations.
        
        Args:
            operation_name: Name of the operation being tracked
            func: Function to execute and track
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
        """
        start_time = time.time()
        operation_id = f"{operation_name}_{int(start_time * 1000)}"
        
        try:
            # Execute function - check if it's a coroutine function
            import asyncio
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Calculate metrics
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            # Record successful operation
            self._record_operation(
                operation_id=operation_id,
                operation_name=operation_name,
                latency_ms=latency_ms,
                success=True,
                timestamp=start_time,
                result_size=self._estimate_result_size(result)
            )
            
            return result
            
        except Exception as e:
            # Record failed operation
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            self._record_operation(
                operation_id=operation_id,
                operation_name=operation_name,
                latency_ms=latency_ms,
                success=False,
                timestamp=start_time,
                error=str(e)
            )
            
            raise
    
    def _record_operation(
        self, 
        operation_id: str,
        operation_name: str,
        latency_ms: float,
        success: bool,
        timestamp: float,
        result_size: int = 0,
        error: Optional[str] = None
    ):
        """Record operation metrics."""
        # Add to latency samples
        self.metrics["latency_samples"].append(latency_ms)
        
        # Record detailed operation
        operation_record = {
            "id": operation_id,
            "operation": operation_name,
            "latency_ms": latency_ms,
            "success": success,
            "timestamp": timestamp,
            "datetime": datetime.fromtimestamp(timestamp).isoformat(),
            "result_size": result_size
        }
        
        if error:
            operation_record["error"] = error
            
        self.metrics["memory_operations"].append(operation_record)
        
        # Log performance warnings
        if latency_ms > self.targets["latency_p95_ms"]:
            logger.warning(f"Operation {operation_name} exceeded p95 target: {latency_ms:.1f}ms")
        elif latency_ms > self.targets["latency_p50_ms"]:
            logger.info(f"Operation {operation_name} exceeded p50 target: {latency_ms:.1f}ms")
    
    def record_token_usage(self, operation: str, tokens: int, conversation_id: str = None):
        """
        Record token usage for operations.
        
        Args:
            operation: Operation name
            tokens: Number of tokens used
            conversation_id: Optional conversation identifier
        """
        token_record = {
            "operation": operation,
            "tokens": tokens,
            "timestamp": time.time(),
            "conversation_id": conversation_id
        }
        
        self.metrics["token_usage"].append(token_record)
        
        # Track conversation-level usage
        if conversation_id:
            if conversation_id not in self.metrics["session_stats"]:
                self.metrics["session_stats"][conversation_id] = {
                    "total_tokens": 0,
                    "operations": 0,
                    "start_time": time.time()
                }
            
            self.metrics["session_stats"][conversation_id]["total_tokens"] += tokens
            self.metrics["session_stats"][conversation_id]["operations"] += 1
            
            # Check conversation token target
            total_tokens = self.metrics["session_stats"][conversation_id]["total_tokens"]
            if total_tokens > self.targets["tokens_per_conversation"]:
                logger.warning(f"Conversation {conversation_id} exceeded token target: {total_tokens}")
    
    def record_accuracy_metric(self, metric_type: str, value: float, baseline: float = None):
        """
        Record accuracy metrics for memory retrieval.
        
        Args:
            metric_type: Type of accuracy metric (e.g., 'retrieval_precision')
            value: Measured accuracy value
            baseline: Optional baseline for improvement calculation
        """
        accuracy_record = {
            "metric_type": metric_type,
            "value": value,
            "baseline": baseline,
            "timestamp": time.time()
        }
        
        if baseline:
            improvement = (value - baseline) / baseline
            accuracy_record["improvement"] = improvement
            
            if improvement < self.targets["accuracy_improvement"]:
                logger.warning(f"Accuracy improvement below target: {improvement:.2%}")
        
        self.metrics["accuracy_metrics"].append(accuracy_record)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get current performance statistics.
        
        Returns:
            Dictionary with performance metrics and target comparison
        """
        if not self.metrics["latency_samples"]:
            return {"status": "no_data"}
        
        # Calculate latency percentiles
        latencies = sorted(self.metrics["latency_samples"])
        p50_idx = int(len(latencies) * 0.5)
        p95_idx = int(len(latencies) * 0.95)
        
        latency_p50 = latencies[p50_idx] if p50_idx < len(latencies) else latencies[-1]
        latency_p95 = latencies[p95_idx] if p95_idx < len(latencies) else latencies[-1]
        
        # Calculate success rate
        total_ops = len(self.metrics["memory_operations"])
        successful_ops = sum(1 for op in self.metrics["memory_operations"] if op["success"])
        success_rate = successful_ops / total_ops if total_ops > 0 else 0
        
        # Token usage stats
        total_tokens = sum(record["tokens"] for record in self.metrics["token_usage"])
        avg_tokens_per_op = total_tokens / len(self.metrics["token_usage"]) if self.metrics["token_usage"] else 0
        
        # Conversation stats
        active_conversations = len(self.metrics["session_stats"])
        avg_conversation_tokens = statistics.mean([
            stats["total_tokens"] for stats in self.metrics["session_stats"].values()
        ]) if self.metrics["session_stats"] else 0
        
        # Performance vs targets
        stats = {
            "performance_metrics": {
                "latency_p50_ms": latency_p50,
                "latency_p95_ms": latency_p95,
                "latency_mean_ms": statistics.mean(latencies),
                "success_rate": success_rate,
                "total_operations": total_ops
            },
            "token_metrics": {
                "total_tokens": total_tokens,
                "avg_tokens_per_operation": avg_tokens_per_op,
                "avg_tokens_per_conversation": avg_conversation_tokens,
                "active_conversations": active_conversations
            },
            "target_comparison": {
                "latency_p50_meets_target": latency_p50 <= self.targets["latency_p50_ms"],
                "latency_p95_meets_target": latency_p95 <= self.targets["latency_p95_ms"],
                "conversation_tokens_meets_target": avg_conversation_tokens <= self.targets["tokens_per_conversation"],
                "p50_vs_target_ratio": latency_p50 / self.targets["latency_p50_ms"],
                "p95_vs_target_ratio": latency_p95 / self.targets["latency_p95_ms"],
                "tokens_vs_target_ratio": avg_conversation_tokens / self.targets["tokens_per_conversation"] if avg_conversation_tokens > 0 else 0
            },
            "session_info": {
                "session_id": self.current_session,
                "uptime_seconds": time.time() - self.session_start,
                "data_points": len(latencies)
            }
        }
        
        return stats
    
    def get_recent_operations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent memory operations.
        
        Args:
            limit: Number of recent operations to return
            
        Returns:
            List of recent operation records
        """
        return self.metrics["memory_operations"][-limit:]
    
    def get_conversation_stats(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get statistics for a specific conversation.
        
        Args:
            conversation_id: Conversation identifier
            
        Returns:
            Conversation statistics or None if not found
        """
        return self.metrics["session_stats"].get(conversation_id)
    
    def reset_metrics(self):
        """Reset all metrics (useful for testing)."""
        self.metrics = {
            "latency_samples": [],
            "token_usage": [],
            "memory_operations": [],
            "accuracy_metrics": [],
            "session_stats": {}
        }
        self.session_start = time.time()
        self.current_session = datetime.now().isoformat()
        logger.info("Reset performance metrics")
    
    def _estimate_result_size(self, result: Any) -> int:
        """
        Estimate the size of operation result.
        
        Args:
            result: Operation result
            
        Returns:
            Estimated size in bytes
        """
        try:
            if isinstance(result, str):
                return len(result.encode('utf-8'))
            elif isinstance(result, (list, dict)):
                return len(str(result).encode('utf-8'))
            elif hasattr(result, '__len__'):
                return len(result)
            else:
                return len(str(result).encode('utf-8'))
        except:
            return 0
    
    def export_metrics(self) -> Dict[str, Any]:
        """
        Export all metrics for analysis.
        
        Returns:
            Complete metrics dictionary
        """
        return {
            "metrics": self.metrics.copy(),
            "targets": self.targets.copy(),
            "performance_stats": self.get_performance_stats(),
            "export_timestamp": time.time()
        }