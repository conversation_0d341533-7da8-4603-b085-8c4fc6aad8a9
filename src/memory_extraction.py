"""
Memory Extraction Module for Two-Phase Pipeline

Processes message pairs (mt-1, mt) with conversation context to extract
salient memories for storage.
"""

import json
import logging
from typing import Tuple, List, Dict, Any, Optional
from llm_cache_service import get_llm_cache_service, CacheType
from config import get_config
from exceptions import MemoryExtractionError, LLMResponseError, LLMTimeoutError

logger = logging.getLogger(__name__)
config = get_config()

class MemoryExtractionModule:
    """
    Extraction phase of the two-phase memory pipeline.
    
    Processes message pairs with context to identify memories worth storing.
    Based on the Mem0 research architecture from arxiv:2504.19413v1.
    """
    
    def __init__(self, llm_client, bge_client):
        """
        Initialize extraction module.
        
        Args:
            llm_client: LLM client for memory extraction
            bge_client: BGE embedding client
        """
        self.llm_client = llm_client
        self.bge_client = bge_client
        self.cache_service = get_llm_cache_service()
        logger.info("Initialized MemoryExtractionModule with LLM caching")
    
    async def extract_memories(
        self, 
        message_pair: Tuple[str, str], 
        context: Dict[str, Any]
    ) -> List[str]:
        """
        Extract salient memories from message pair with context.
        
        This implements the extraction phase of the two-phase pipeline.
        
        Args:
            message_pair: Tuple of (previous_message, current_message)
            context: Context dictionary containing:
                - conversation_summary: Current conversation summary
                - recent_messages: List of recent message contents
                - user_id: User identifier
                
        Returns:
            List of extracted memory strings
        """
        try:
            previous_msg, current_msg = message_pair
            
            # Build extraction prompt based on research
            prompt = self._build_extraction_prompt(
                previous_msg, current_msg, context
            )
            
            # Check cache first for performance optimization
            cache_context = {
                "user_id": context.get("user_id", "unknown"),
                "summary_length": len(context.get("conversation_summary", "")),
                "recent_count": len(context.get("recent_messages", []))
            }
            
            cached_response = await self.cache_service.get_cached_response(
                CacheType.EXTRACTION, prompt, cache_context
            )
            
            if cached_response:
                logger.debug("Using cached extraction response")
                response = cached_response
            else:
                # Generate extraction using LLM
                logger.debug("Generating new extraction response")
                response = await self.llm_client.generate(prompt)
                
                # Cache the response for future use
                if response and response.strip():
                    await self.cache_service.cache_response(
                        CacheType.EXTRACTION, prompt, response, cache_context
                    )
            
            # Parse response with enhanced fallback for Gemini models
            memories = self._parse_llm_response(response)
            
            # Filter and clean memories
            cleaned_memories = self._filter_memories(memories)
            
            logger.debug(f"Extracted {len(cleaned_memories)} memories from message pair")
            return cleaned_memories
            
        except (MemoryExtractionError, LLMResponseError, LLMTimeoutError) as e:
            logger.error(f"Memory extraction failed: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error in memory extraction: {e}")
            raise MemoryExtractionError(
                f"Memory extraction failed due to unexpected error: {str(e)}",
                llm_response=response if 'response' in locals() else None
            )
    
    def _build_extraction_prompt(
        self, 
        previous_msg: str, 
        current_msg: str, 
        context: Dict[str, Any]
    ) -> str:
        """
        Build the extraction prompt based on Mem0 research patterns.
        Enhanced with stricter JSON formatting for Gemini models.
        
        Args:
            previous_msg: Previous message in the conversation
            current_msg: Current message in the conversation
            context: Conversation context
            
        Returns:
            Formatted extraction prompt
        """
        conversation_summary = context.get('conversation_summary', 'None')
        recent_messages = context.get('recent_messages', [])
        user_id = context.get('user_id', 'unknown')
        
        # Build recent context
        recent_context = "\\n".join(recent_messages[-5:]) if recent_messages else "None"
        
        prompt = f"""Extract factual memories from this conversation that should be stored for future reference.

FOCUS ON:
- User preferences, background, skills, expertise
- Important decisions, conclusions, commitments
- Significant events, experiences, or facts
- Technical knowledge or project information

INPUT:
Previous Message: {previous_msg}
Current Message: {current_msg}
Conversation Summary: {conversation_summary}
Recent Context: {recent_context}

EXTRACT RULES:
- Only factual, actionable information valuable for future conversations
- Skip temporary states, generic responses, or redundant information
- Each memory must be a complete, standalone sentence
- Maximum 5 memories per extraction

CRITICAL: You MUST respond with ONLY a valid JSON array of strings. No other text, explanations, or formatting.

Valid examples:
["User prefers React over Vue for frontend development"]
["User works as a software engineer at TechCorp", "User specializes in Python and machine learning"]
[]

Response (JSON array only):"""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[str]:
        """
        Enhanced LLM response parsing with robust fallback for Gemini models.
        
        Args:
            response: Raw LLM response string
            
        Returns:
            List of extracted memory strings
        """
        if not response or not response.strip():
            logger.warning("Empty LLM response")
            return []
        
        response = response.strip()
        
        # Method 1: Try direct JSON parsing
        try:
            memories = json.loads(response)
            if isinstance(memories, list):
                logger.debug("Successfully parsed JSON list")
                return [str(m) for m in memories if m and str(m).strip()]
            elif isinstance(memories, str):
                logger.debug("Parsed JSON string, converting to list")
                return [memories] if memories.strip() else []
            else:
                logger.warning(f"JSON parsed but unexpected type: {type(memories)}")
                return [str(memories)] if str(memories).strip() else []
        except json.JSONDecodeError:
            pass
        
        # Method 2: Extract JSON array from response with surrounding text
        import re
        json_match = re.search(r'\[.*?\]', response, re.DOTALL)
        if json_match:
            try:
                memories = json.loads(json_match.group())
                if isinstance(memories, list):
                    logger.debug("Extracted JSON array from response text")
                    return [str(m) for m in memories if m and str(m).strip()]
            except json.JSONDecodeError:
                pass
        
        # Method 3: Parse lines that look like list items
        lines = response.split('\n')
        memories = []
        for line in lines:
            line = line.strip()
            # Remove common list prefixes and quotes
            line = re.sub(r'^[-*•]\s*', '', line)  # Remove bullet points
            line = re.sub(r'^"\s*|"\s*,?\s*$', '', line)  # Remove quotes
            line = re.sub(r'^\d+\.\s*', '', line)  # Remove numbered lists
            
            if line and len(line) > config.memory.MIN_MEMORY_LENGTH and not line.lower().startswith(('here', 'the', 'response')):
                memories.append(line)
        
        if memories:
            logger.debug(f"Extracted {len(memories)} memories from line parsing")
            return memories
        
        # Method 4: Treat entire response as single memory if it looks meaningful
        if len(response) > config.memory.MIN_MEANINGFUL_CONTENT_LENGTH and not any(word in response.lower() for word in ['sorry', 'cannot', 'unable', 'error']):
            # Clean up the response
            cleaned = re.sub(r'^["\[\{]|["\]\}]$', '', response)  # Remove surrounding brackets/quotes
            cleaned = cleaned.strip()
            if cleaned:
                logger.debug("Treating cleaned response as single memory")
                return [cleaned]
        
        logger.warning(f"Failed to parse LLM response with all methods: {response[:config.memory.SUMMARY_PREVIEW_LENGTH]}...")
        return []
    
    def _filter_memories(self, memories: List[str]) -> List[str]:
        """
        Filter and clean extracted memories.
        
        Args:
            memories: Raw extracted memories
            
        Returns:
            Filtered and cleaned memories
        """
        cleaned = []
        
        for memory in memories:
            if not isinstance(memory, str):
                memory = str(memory)
            
            # Clean and validate memory
            memory = memory.strip()
            
            # Skip empty or too short memories
            if len(memory) < config.memory.MIN_MEMORY_LENGTH:
                continue
            
            # Skip generic responses
            if any(pattern in memory.lower() for pattern in config.memory.GENERIC_RESPONSE_PATTERNS):
                continue
            
            # Skip if memory is too long (likely contains conversation flow)
            if len(memory) > config.memory.MAX_MEMORY_LENGTH:
                continue
            
            cleaned.append(memory)
        
        return cleaned
    
    async def extract_from_single_message(
        self, 
        message: str, 
        context: Dict[str, Any]
    ) -> List[str]:
        """
        Extract memories from a single message (fallback for when no previous message).
        
        Args:
            message: Single message to extract from
            context: Conversation context
            
        Returns:
            List of extracted memory strings
        """
        # Create a message pair with empty previous message
        message_pair = ("", message)
        return await self.extract_memories(message_pair, context)
    
    async def extract_with_embedding_context(
        self, 
        message_pair: Tuple[str, str], 
        context: Dict[str, Any],
        similar_memories: List[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Extract memories with additional context from similar existing memories.
        
        Args:
            message_pair: Tuple of (previous_message, current_message)
            context: Conversation context
            similar_memories: List of similar existing memories for context
            
        Returns:
            List of extracted memory strings
        """
        if similar_memories:
            # Add similar memories to context for extraction
            similar_content = [mem['content'] for mem in similar_memories[:3]]
            context = context.copy()
            context['similar_memories'] = similar_content
        
        return await self.extract_memories(message_pair, context)