"""
Custom Exception Classes for Spark Memory MCP Server

Provides specific exception types to replace generic Exception handling
throughout the codebase, improving error diagnostics and handling.
"""

from typing import Optional, Dict, Any


class SparkMemoryError(Exception):
    """Base exception for all Spark Memory system errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the base exception.
        
        Args:
            message: Error message
            details: Optional dictionary with additional error context
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary format for JSON serialization."""
        return {
            "error_type": self.__class__.__name__,
            "message": self.message,
            "details": self.details
        }


# Memory Processing Exceptions

class MemoryExtractionError(SparkMemoryError):
    """Raised when memory extraction from message pairs fails."""
    
    def __init__(self, message: str, llm_response: Optional[str] = None, 
                 parsing_method: Optional[str] = None):
        super().__init__(message, {
            "llm_response": llm_response,
            "parsing_method": parsing_method
        })


class MemoryUpdateError(SparkMemoryError):
    """Raised when memory update operations fail."""
    
    def __init__(self, message: str, candidate_memory: Optional[str] = None,
                 operation: Optional[str] = None, user_id: Optional[str] = None):
        super().__init__(message, {
            "candidate_memory": candidate_memory,
            "operation": operation,
            "user_id": user_id
        })


class MemoryValidationError(SparkMemoryError):
    """Raised when memory content validation fails."""
    
    def __init__(self, message: str, memory_content: Optional[str] = None,
                 validation_rule: Optional[str] = None):
        super().__init__(message, {
            "memory_content": memory_content,
            "validation_rule": validation_rule
        })


# Embedding Service Exceptions

class EmbeddingServiceError(SparkMemoryError):
    """Base exception for BGE embedding service errors."""
    pass


class EmbeddingGenerationError(EmbeddingServiceError):
    """Raised when embedding generation fails."""
    
    def __init__(self, message: str, text_input: Optional[str] = None,
                 bge_server_url: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message, {
            "text_input": text_input[:100] + "..." if text_input and len(text_input) > 100 else text_input,
            "bge_server_url": bge_server_url,
            "status_code": status_code
        })


class EmbeddingServiceUnavailableError(EmbeddingServiceError):
    """Raised when BGE embedding service is not available."""
    
    def __init__(self, message: str, server_url: Optional[str] = None,
                 health_check_failed: bool = False):
        super().__init__(message, {
            "server_url": server_url,
            "health_check_failed": health_check_failed
        })


class EmbeddingDimensionMismatchError(EmbeddingServiceError):
    """Raised when embedding dimensions don't match expected values."""
    
    def __init__(self, message: str, expected_dim: Optional[int] = None,
                 actual_dim: Optional[int] = None):
        super().__init__(message, {
            "expected_dimension": expected_dim,
            "actual_dimension": actual_dim
        })


# Database Exceptions

class DatabaseError(SparkMemoryError):
    """Base exception for database-related errors."""
    pass


class DatabaseConnectionError(DatabaseError):
    """Raised when database connection fails."""
    
    def __init__(self, message: str, database_url: Optional[str] = None,
                 connection_timeout: Optional[float] = None):
        # Sanitize database URL to remove credentials
        sanitized_url = None
        if database_url:
            # Keep only the host and database name for debugging
            try:
                from urllib.parse import urlparse
                parsed = urlparse(database_url)
                sanitized_url = f"{parsed.scheme}://{parsed.hostname}:{parsed.port}{parsed.path}"
            except:
                sanitized_url = "invalid_url"
        
        super().__init__(message, {
            "database_url": sanitized_url,
            "connection_timeout": connection_timeout
        })


class DatabaseQueryError(DatabaseError):
    """Raised when database queries fail."""
    
    def __init__(self, message: str, query: Optional[str] = None,
                 query_timeout: Optional[float] = None, user_id: Optional[str] = None):
        super().__init__(message, {
            "query": query[:200] + "..." if query and len(query) > 200 else query,
            "query_timeout": query_timeout,
            "user_id": user_id
        })


class VectorSearchError(DatabaseError):
    """Raised when vector similarity search fails."""
    
    def __init__(self, message: str, embedding_dimension: Optional[int] = None,
                 threshold: Optional[float] = None, limit: Optional[int] = None):
        super().__init__(message, {
            "embedding_dimension": embedding_dimension,
            "threshold": threshold,
            "limit": limit
        })


# LLM Processing Exceptions

class LLMError(SparkMemoryError):
    """Base exception for LLM processing errors."""
    pass


class LLMConfigurationError(LLMError):
    """Raised when LLM configuration is invalid."""
    
    def __init__(self, message: str, provider: Optional[str] = None,
                 model: Optional[str] = None, missing_config: Optional[str] = None):
        super().__init__(message, {
            "provider": provider,
            "model": model,
            "missing_config": missing_config
        })


class LLMResponseError(LLMError):
    """Raised when LLM returns invalid or unexpected responses."""
    
    def __init__(self, message: str, prompt: Optional[str] = None,
                 response: Optional[str] = None, expected_format: Optional[str] = None):
        super().__init__(message, {
            "prompt": prompt[:200] + "..." if prompt and len(prompt) > 200 else prompt,
            "response": response[:200] + "..." if response and len(response) > 200 else response,
            "expected_format": expected_format
        })


class LLMTimeoutError(LLMError):
    """Raised when LLM operations timeout."""
    
    def __init__(self, message: str, timeout_duration: Optional[float] = None,
                 operation_type: Optional[str] = None):
        super().__init__(message, {
            "timeout_duration": timeout_duration,
            "operation_type": operation_type
        })


# Configuration Exceptions

class ConfigurationError(SparkMemoryError):
    """Base exception for configuration-related errors."""
    pass


class ConfigurationValidationError(ConfigurationError):
    """Raised when configuration validation fails."""
    
    def __init__(self, message: str, config_key: Optional[str] = None,
                 config_value: Optional[Any] = None, validation_rule: Optional[str] = None):
        super().__init__(message, {
            "config_key": config_key,
            "config_value": str(config_value) if config_value is not None else None,
            "validation_rule": validation_rule
        })


class EnvironmentVariableError(ConfigurationError):
    """Raised when required environment variables are missing or invalid."""
    
    def __init__(self, message: str, variable_name: Optional[str] = None,
                 variable_value: Optional[str] = None):
        super().__init__(message, {
            "variable_name": variable_name,
            "variable_value": variable_value
        })


# Server Lifecycle Exceptions

class ServerError(SparkMemoryError):
    """Base exception for server lifecycle errors."""
    pass


class ServerInitializationError(ServerError):
    """Raised when server initialization fails."""
    
    def __init__(self, message: str, initialization_phase: Optional[str] = None,
                 component: Optional[str] = None):
        super().__init__(message, {
            "initialization_phase": initialization_phase,
            "component": component
        })


class ServerNotReadyError(ServerError):
    """Raised when operations are attempted before server is ready."""
    
    def __init__(self, message: str, requested_operation: Optional[str] = None,
                 readiness_status: Optional[Dict[str, Any]] = None):
        super().__init__(message, {
            "requested_operation": requested_operation,
            "readiness_status": readiness_status
        })


class MCPProtocolError(ServerError):
    """Raised when MCP protocol operations fail."""
    
    def __init__(self, message: str, mcp_tool: Optional[str] = None,
                 context_error: Optional[str] = None):
        super().__init__(message, {
            "mcp_tool": mcp_tool,
            "context_error": context_error
        })


# Performance and Monitoring Exceptions

class PerformanceError(SparkMemoryError):
    """Base exception for performance-related errors."""
    pass


class PerformanceThresholdExceededError(PerformanceError):
    """Raised when performance thresholds are exceeded."""
    
    def __init__(self, message: str, metric_name: Optional[str] = None,
                 actual_value: Optional[float] = None, threshold_value: Optional[float] = None):
        super().__init__(message, {
            "metric_name": metric_name,
            "actual_value": actual_value,
            "threshold_value": threshold_value
        })


class CacheError(SparkMemoryError):
    """Base exception for cache-related errors."""
    pass


class CacheOperationError(CacheError):
    """Raised when cache operations fail."""
    
    def __init__(self, message: str, cache_type: Optional[str] = None,
                 operation: Optional[str] = None, cache_key: Optional[str] = None):
        super().__init__(message, {
            "cache_type": cache_type,
            "operation": operation,
            "cache_key": cache_key
        })


# Utility Functions

def handle_known_exception(exception: Exception) -> SparkMemoryError:
    """
    Convert known exception types to appropriate Spark Memory exceptions.
    
    Args:
        exception: The original exception to convert
        
    Returns:
        Appropriate SparkMemoryError subclass
    """
    error_message = str(exception)
    
    # Database-related exceptions
    if "connection" in error_message.lower() and "database" in error_message.lower():
        return DatabaseConnectionError(f"Database connection failed: {error_message}")
    
    if "timeout" in error_message.lower():
        if "database" in error_message.lower():
            return DatabaseQueryError(f"Database query timeout: {error_message}")
        elif "embedding" in error_message.lower() or "bge" in error_message.lower():
            return EmbeddingGenerationError(f"Embedding generation timeout: {error_message}")
        else:
            return LLMTimeoutError(f"Operation timeout: {error_message}")
    
    # HTTP/Network related exceptions
    if "http" in error_message.lower() or "connection" in error_message.lower():
        return EmbeddingServiceUnavailableError(f"Service unavailable: {error_message}")
    
    # JSON parsing errors
    if "json" in error_message.lower() or "parse" in error_message.lower():
        return LLMResponseError(f"Response parsing failed: {error_message}")
    
    # Configuration errors
    if "environment" in error_message.lower() or "config" in error_message.lower():
        return ConfigurationError(f"Configuration error: {error_message}")
    
    # Default to base exception
    return SparkMemoryError(f"Unhandled error: {error_message}")


def create_error_response(exception: SparkMemoryError, function_name: str) -> Dict[str, Any]:
    """
    Create a standardized error response dictionary.
    
    Args:
        exception: The SparkMemoryError exception
        function_name: Name of the function where the error occurred
        
    Returns:
        Standardized error response dictionary
    """
    error_dict = exception.to_dict()
    error_dict["function"] = function_name
    error_dict["success"] = False
    
    # Add retry recommendations based on error type
    if isinstance(exception, (EmbeddingServiceUnavailableError, DatabaseConnectionError)):
        error_dict["retry_recommended"] = True
        error_dict["retry_after"] = 5
    elif isinstance(exception, (LLMTimeoutError, DatabaseQueryError)):
        error_dict["retry_recommended"] = True
        error_dict["retry_after"] = 3
    else:
        error_dict["retry_recommended"] = False
    
    return error_dict