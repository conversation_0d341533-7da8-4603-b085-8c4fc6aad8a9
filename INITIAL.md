# Memory Intelligence Enhancement Features

## FEATURE:

Implement a comprehensive memory intelligence system with four key enhancements to the existing Spark Memory MCP Server:

1. **Confidence Scores**: Add confidence scoring to memory similarity search results based on vector distance, embedding quality, and temporal relevance
2. **Memory Aging**: Implement time-based confidence decay where memory relevance decreases over time with configurable aging curves
3. **Memory Merging**: Detect and merge semantically similar memories to reduce redundancy and consolidate related information
4. **Access Pattern Tracking**: Monitor and analyze memory retrieval patterns to optimize storage, improve recommendations, and identify frequently accessed memories

**Technical Requirements:**
- PostgreSQL with pgvector extension for vector similarity search
- BGE embeddings (768 dimensions) with L2 distance metrics
- Python async/await pattern for performance
- MCP (Model Context Protocol) interface compatibility
- Configurable parameters for aging rates, similarity thresholds, and merge criteria

## EXAMPLES:

No example files are currently available in the examples/ folder. Implementation should follow the existing project patterns found in:

- `src/memory_database.py` - Database operations with pgvector
- `src/memory_extraction.py` - Two-phase memory pipeline 
- `src/enhanced_database_service.py` - Connection pooling and performance
- `src/bge_embedding_client.py` - BGE embedding generation

**Reference Implementation Patterns:**
Based on the enhanced knowledge base research, key implementation patterns include:

1. **Mem0 Memory Management** - Comprehensive memory operations with search, update, and history tracking
2. **Supabase pgvector Functions** - SQL functions for semantic similarity search with distance operators
3. **MCP Structured Output** - Using Pydantic models for rich data structures and tool validation
4. **Vector Database Best Practices** - PostgreSQL embedding storage with proper indexing strategies

## DOCUMENTATION:

### Vector Similarity and Confidence Scoring
Based on research from authoritative sources including Pinecone, pgvector documentation, and PostgreSQL vector database implementations:

**Similarity Metrics:**
- **L2 Distance (Euclidean)**: Currently used in the system, optimal for BGE embeddings
- **Cosine Similarity**: Alternative metric that normalizes vector length differences
- **Dot Product**: Captures both similarity and magnitude, useful for popularity weighting

**Confidence Score Implementation:**
```python
def calculate_confidence_score(distance: float, max_distance: float = 2.0) -> float:
    """Convert L2 distance to confidence score (0-1 range)"""
    return max(0.0, 1.0 - (distance / max_distance))
```

**Enhanced Vector Search with Scores (Based on Supabase pgvector patterns):**
```sql
-- Function based on Supabase semantic search documentation
CREATE OR REPLACE FUNCTION search_memories_with_confidence(
    query_embedding vector(768),
    user_id TEXT,
    match_threshold FLOAT DEFAULT 0.7,
    match_count INT DEFAULT 10
) RETURNS TABLE (
    id UUID,
    content TEXT,
    distance FLOAT,
    confidence_score FLOAT,
    created_at TIMESTAMP,
    access_count INTEGER
) LANGUAGE sql AS $$
    SELECT id, content, 
           embedding <-> query_embedding as distance,
           GREATEST(0.0, 1.0 - (embedding <-> query_embedding)) as confidence_score,
           created_at, access_count
    FROM memories 
    WHERE user_id = $2
    AND embedding <-> query_embedding < (1.0 - match_threshold)
    ORDER BY embedding <-> query_embedding ASC
    LIMIT LEAST(match_count, 50);
$$;
```

### Memory Aging and Temporal Decay
Research indicates multiple approaches for time-based confidence decay. Implementation follows cognitive psychology models for memory retention:

**Exponential Decay Model (Ebbinghaus Forgetting Curve):**
```python
import math
from datetime import datetime, timedelta
from typing import Dict, Any
from pydantic import BaseModel, Field

class MemoryAgingConfig(BaseModel):
    """Configuration for memory aging based on Mem0 config patterns"""
    half_life_days: float = Field(default=30.0, description="Half-life for exponential decay")
    linear_decay_rate: float = Field(default=0.02, description="Daily decay rate for linear model")
    aging_model: str = Field(default="exponential", description="exponential or linear")
    min_confidence: float = Field(default=0.1, description="Minimum confidence threshold")

def apply_temporal_decay(base_confidence: float, created_at: datetime, 
                        config: MemoryAgingConfig) -> float:
    """Apply temporal decay to confidence score using configurable models"""
    days_elapsed = (datetime.utcnow() - created_at).total_seconds() / 86400
    
    if config.aging_model == "exponential":
        decay_factor = math.exp(-0.693 * days_elapsed / config.half_life_days)
        decayed_confidence = base_confidence * decay_factor
    elif config.aging_model == "linear":
        decay_amount = config.linear_decay_rate * days_elapsed
        decayed_confidence = base_confidence - decay_amount
    else:
        return base_confidence
    
    return max(config.min_confidence, decayed_confidence)
```

**Enhanced Memory Aging with Access Pattern Boost:**
```python
def apply_enhanced_aging(memory_record: Dict[str, Any], 
                        config: MemoryAgingConfig) -> float:
    """Apply aging with access pattern considerations"""
    base_confidence = memory_record.get('base_confidence', 1.0)
    created_at = memory_record['created_at']
    access_count = memory_record.get('access_count', 0)
    last_accessed = memory_record.get('last_accessed_at')
    
    # Apply temporal decay
    decayed_confidence = apply_temporal_decay(base_confidence, created_at, config)
    
    # Access frequency boost (logarithmic scale)
    if access_count > 0:
        access_boost = min(0.2, math.log(access_count + 1) * 0.05)
        decayed_confidence += access_boost
    
    # Recent access boost
    if last_accessed:
        days_since_access = (datetime.utcnow() - last_accessed).total_seconds() / 86400
        if days_since_access < 7:  # Recent access within week
            recency_boost = (7 - days_since_access) / 35  # Max 0.2 boost
            decayed_confidence += recency_boost
    
    return min(1.0, max(config.min_confidence, decayed_confidence))
```

### Memory Merging and Deduplication
Based on entity resolution techniques and PostgreSQL pgvector capabilities:

**Similarity-Based Merging:**
```python
async def find_similar_memories(self, new_memory: Dict, threshold: float = 0.85) -> List[Dict]:
    """Find existing memories similar to new memory for potential merging"""
    query = """
    SELECT id, content, embedding <-> %s as distance,
           created_at, updated_at, access_count
    FROM memories 
    WHERE user_id = %s 
    AND embedding <-> %s < %s
    ORDER BY embedding <-> %s;
    """
    # similarity_threshold = 1.0 - threshold converts confidence to distance
    similarity_threshold = 1.0 - threshold
    
    return await self.db_service.fetch_all(
        query, (new_memory['embedding'], user_id, new_memory['embedding'], 
                similarity_threshold, new_memory['embedding'])
    )
```

**Merge Strategy:**
```python
async def merge_memories(self, target_id: str, source_memories: List[Dict]) -> Dict:
    """Merge multiple similar memories into target memory"""
    # Combine content with deduplication
    combined_content = self._merge_content(target_memory, source_memories)
    
    # Update metadata (access counts, timestamps)
    merged_access_count = sum(mem['access_count'] for mem in source_memories)
    latest_update = max(mem['updated_at'] for mem in source_memories)
    
    # Create new embedding for merged content
    merged_embedding = await self.bge_client.get_embedding(combined_content)
    
    # Update target and delete sources
    await self._update_merged_memory(target_id, combined_content, merged_embedding, 
                                   merged_access_count, latest_update)
    await self._delete_source_memories([mem['id'] for mem in source_memories])
```

### Access Pattern Tracking
Research from memory access pattern analysis and machine learning systems:

**Database Schema Extension:**
```sql
-- Add access tracking columns to memories table
ALTER TABLE memories ADD COLUMN IF NOT EXISTS access_count INTEGER DEFAULT 0;
ALTER TABLE memories ADD COLUMN IF NOT EXISTS last_accessed_at TIMESTAMP;
ALTER TABLE memories ADD COLUMN IF NOT EXISTS access_pattern JSONB DEFAULT '{}';

-- Create access log table for detailed analytics
CREATE TABLE IF NOT EXISTS memory_access_log (
    id SERIAL PRIMARY KEY,
    memory_id UUID NOT NULL,
    user_id TEXT NOT NULL,
    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    query_similarity FLOAT,
    context_metadata JSONB
);
```

**Access Pattern Implementation:**
```python
async def track_memory_access(self, memory_id: str, query: str, context: Dict):
    """Track memory access for pattern analysis"""
    
    # Update memory access metadata
    await self.db_service.execute(
        """UPDATE memories 
           SET access_count = access_count + 1,
               last_accessed_at = CURRENT_TIMESTAMP,
               access_pattern = COALESCE(access_pattern, '{}') || %s
           WHERE id = %s""",
        (json.dumps({"last_query": query, "context": context}), memory_id)
    )
    
    # Log detailed access information
    query_embedding = await self.bge_client.get_embedding(query)
    similarity = await self._calculate_query_similarity(memory_id, query_embedding)
    
    await self.db_service.execute(
        """INSERT INTO memory_access_log 
           (memory_id, user_id, query_similarity, context_metadata)
           VALUES (%s, %s, %s, %s)""",
        (memory_id, context.get('user_id'), similarity, json.dumps(context))
    )
```

## OTHER CONSIDERATIONS:

### Performance and Scalability Gotchas

**Vector Index Optimization:**
- pgvector requires proper indexing for performance: `CREATE INDEX ON memories USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);`
- Index maintenance becomes critical with frequent updates from confidence score recalculation
- Consider batch updates for aging calculations to minimize index rebuilds

**Memory Merging Complexity:**
- Similarity threshold tuning is critical - too low creates noise, too high misses opportunities
- Semantic content merging requires sophisticated text combination strategies
- Embedding recalculation for merged content is computationally expensive
- Race conditions possible with concurrent merge operations

**Access Pattern Storage:**
- JSONB columns can become large with detailed access tracking
- Consider data retention policies for access logs to prevent unbounded growth
- Aggregation strategies needed for pattern analysis (hourly/daily rollups)

### Configuration Management

**Aging Parameters:**
- Half-life values highly domain-dependent (hours for chat, months for knowledge bases)
- Multiple aging curves may be needed (immediate vs long-term memory)
- Consider user-specific aging preferences

**Similarity Thresholds:**
- BGE embedding similarities typically range 0.3-0.95 for related content
- Different thresholds needed for merging (high, ~0.85) vs retrieval (medium, ~0.7)
- Threshold calibration requires domain-specific testing

**Access Pattern Analytics:**
- Pattern detection algorithms need minimum access count thresholds
- Temporal pattern analysis requires sufficient historical data
- Privacy considerations for user behavior tracking

### Integration with Existing Architecture

**Two-Phase Pipeline Compatibility:**
- Confidence scoring integrates naturally with existing similarity search
- Memory aging affects both extraction and update phases
- Merging operations should occur during update phase to maintain consistency
- Access tracking fits into existing retrieval workflows

**Database Migration Requirements:**
- Schema changes need careful migration strategy for production systems
- Index creation can be time-intensive on large datasets
- Confidence score recalculation may require full dataset processing

**MCP Interface Extensions:**
Based on MCP SDK structured output patterns from the knowledge base:

```python
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class MemorySearchResult(BaseModel):
    """Structured output for memory search with confidence scoring"""
    id: str = Field(description="Memory unique identifier")
    content: str = Field(description="Memory content")
    confidence_score: float = Field(description="Confidence score (0.0-1.0)")
    distance: float = Field(description="Vector distance")
    created_at: datetime = Field(description="Creation timestamp")
    access_count: int = Field(default=0, description="Number of times accessed")
    aging_factor: float = Field(description="Temporal decay factor")

class MemoryMergeCandidate(BaseModel):
    """Structured output for memory merge candidates"""
    source_id: str = Field(description="Source memory ID to merge")
    target_id: str = Field(description="Target memory ID to merge into")
    similarity_score: float = Field(description="Similarity score (0.0-1.0)")
    confidence_delta: float = Field(description="Confidence improvement from merge")
    merge_strategy: str = Field(description="Recommended merge strategy")

class AccessPatternAnalytics(BaseModel):
    """Structured output for access pattern analytics"""
    memory_id: str = Field(description="Memory identifier")
    total_accesses: int = Field(description="Total access count")
    avg_query_similarity: float = Field(description="Average query similarity")
    access_frequency_trend: str = Field(description="increasing, stable, or decreasing")
    peak_access_hours: List[int] = Field(description="Hours with most access activity")
    context_patterns: Dict[str, Any] = Field(description="Common access contexts")

# MCP Tool Implementation Examples
@mcp.tool()
async def search_memories_with_confidence(
    query: str, 
    user_id: str,
    confidence_threshold: float = 0.7,
    include_aging: bool = True
) -> List[MemorySearchResult]:
    """Enhanced memory search with confidence scoring and aging"""
    # Implementation follows existing memory_database.py patterns
    pass

@mcp.tool()
async def get_merge_candidates(
    user_id: str,
    similarity_threshold: float = 0.85,
    max_candidates: int = 10
) -> List[MemoryMergeCandidate]:
    """Find memory merge candidates based on similarity"""
    # Implementation follows pgvector similarity patterns
    pass

@mcp.tool()
async def analyze_access_patterns(
    user_id: str,
    memory_id: Optional[str] = None,
    time_window_days: int = 30
) -> List[AccessPatternAnalytics]:
    """Analyze memory access patterns for optimization"""
    # Implementation follows access tracking patterns
    pass
```

- **Tool Categories**: confidence-aware search, aging parameter controls, merge operations, analytics reporting
- **Structured Output**: All tools return Pydantic models for rich data validation and client integration
- **Async Interface**: Compatible with existing MCP server async patterns
- **Configuration Integration**: Tools accept configurable thresholds and parameters